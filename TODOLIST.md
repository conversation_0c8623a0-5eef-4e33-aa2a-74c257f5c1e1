# TODO LIST

- [X] Navbar : Rendre Dynamique le nom du site
- [X] Footer : Rendre Dynamique la date (année) du copyright
- [ ] Page : Team Dynamique (on a un tableau d'objet en JS on doit l'afficher dans le template)
- [ ] Page : A propos (Une page qui vous présente de manière stylée #lameilleureversiondemoimeme), template (strict minimum DaisyUI),les datas sont en JS of course
- [ ] Page Contact (Un formulaire de contact qui envoi un mail à Jefff)
- [ ] Page Skills (une page qui liste les skills (cf tab competences cda titre) avec des liens vers les leçons correspondantes)
- [ ] Outil pour Jefff : Une page compteur de mensonge (persistant)
- [ ] Outil pour Jefff : Une page De group Generator (équitable + persistant + import clipBoard)
- [ ] Saturn
- [ ] Uranus
- [ ] Neptune
- [ ] Comet Haley

## Des Idees de pages ou Features

- [ ] README.md : expliquer bien le projet, lien stylé github, stackblitz, codepen
- [ ] Page (/lessons/setup): Setup Vite (la page explique comment démmarrer un projet JS/TS avec Vite parce que c est cool) avec explication des fichiers et des dossiers spécifiques (package.json, vite.config.js, node_modules, dist, src, index.html, main.js, style.css, etc.)
- [ ] IA-PROMPT.md : Centralisation des prompt. (No Cheat JS, plutot pour créer du template HTML avec Classes DaisyUI)
- [ ] IA-AGENTS.md : Centralisation des Agents IA (contexte sur).
- [ ] Page Progression (Timeline)
- [ ] Faire pieuvre
- [ ] Earth (Orbit/Moon)
- [ ] Mars
- [ ] Jupiter
- [ ] Saturn
- [ ] Uranus
- [ ] Neptune
- [ ] Comet Haley

## Mardi 2/09

- [x] Présentation Nouveau Setup
  Stack : NPM, Vite, Tailwind, DaisyUI, DomPurify.
  Présentation du projet : Un BANGER app JS (mon hub de cours JS)
- [x] Question Random Dom Selectors
- [X] Rev : JSDoc l'art de bien faire des commentaires
- [X] Rev : Fonctions
- [X] DOM : Events

## Demain 3/09

- [ ] Mettre sur Github
- [ ] Rev : Boucles
- [ ] DOM : Attack regex
- [ ] Installer des librairies
- [ ] Jupiter
- [ ] Saturn
- [ ] Uranus
- [ ] Neptune
- [ ] Comet Haley
