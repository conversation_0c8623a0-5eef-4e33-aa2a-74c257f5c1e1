<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div class="navbar bg-base-100 shadow-lg sticky top-0 z-50">
          <div class="navbar-start">
            <div class="dropdown">
              <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
                </svg>
              </div>
              <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
                <li><a href="/">Accueil</a></li>
                <li><a href="/src/pages/lessons/" class="hover:bg-base-200 transition-colors">Leçons</a></li>
                <li><a href="/src/pages/exo/" class="hover:bg-base-200 transition-colors">Exercices</a></li>
                <li><a href="/src/pages/tp/" class="hover:bg-base-200 transition-colors">TP</a></li>
                <li><a>À propos</a></li>
                <li><a>Contact</a></li>
              </ul>
            </div>
            <a href="/" id="site-name-navbar" class="btn btn-ghost text-lg sm:text-xl"></a>
          </div>
          <div class="navbar-center hidden lg:flex">
            <ul class="menu menu-horizontal px-1">
              <li><a class="hover:bg-base-200 transition-colors">Accueil</a></li>
              <li><a href="/src/pages/lessons/" class="hover:bg-base-200 transition-colors">Leçons</a></li>
              <li><a href="/src/pages/exo/" class="hover:bg-base-200 transition-colors">Exercices</a></li>
              <li><a href="/src/pages/tp/" class="hover:bg-base-200 transition-colors">TP</a></li>
              <li><a href="/src/pages/contact/" class="hover:bg-base-200 transition-colors">Contact</a></li>
            </ul>
          </div>
          <div class="navbar-end">
            <a class="btn btn-primary btn-sm sm:btn-md">Connexion</a>
          </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div class="hero min-h-[60vh] sm:min-h-[70vh] lg:min-h-96 bg-gradient-to-br from-base-200 to-base-300 rounded-lg">
            <div class="hero-content text-center">
              <div class="max-w-xs sm:max-w-md lg:max-w-lg">
                <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">Bienvenue!</h1>
                <p class="py-4 sm:py-6 text-sm sm:text-base">Votre contenu principal va ici. Cette section est responsive et s'adapte à tous les écrans.</p>
                <button class="btn btn-primary btn-sm sm:btn-md lg:btn-lg">Commencer</button>
              </div>
            </div>
          </div>
          
          <!-- Content Cards -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mt-6 sm:mt-8">
            <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
              <div class="card-body p-4 sm:p-6">
                <h2 class="card-title text-lg sm:text-xl">Service 1</h2>
                <p class="text-sm sm:text-base">Description de votre premier service.</p>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-primary btn-sm sm:btn-md">En savoir plus</button>
                </div>
              </div>
            </div>
            <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
              <div class="card-body p-4 sm:p-6">
                <h2 class="card-title text-lg sm:text-xl">Service 2</h2>
                <p class="text-sm sm:text-base">Description de votre deuxième service.</p>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-primary btn-sm sm:btn-md">En savoir plus</button>
                </div>
              </div>
            </div>
            <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow sm:col-span-2 lg:col-span-1">
              <div class="card-body p-4 sm:p-6">
                <h2 class="card-title text-lg sm:text-xl">Service 3</h2>
                <p class="text-sm sm:text-base">Description de votre troisième service.</p>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-primary btn-sm sm:btn-md">En savoir plus</button>
                </div>
              </div>
            </div>
          </div>
        </main>

        <!-- Footer -->
        <footer class="footer footer-center bg-base-200 text-base-content rounded-t-lg p-6 sm:p-10 mt-auto">
          <nav class="grid grid-flow-col gap-2 sm:gap-4 text-xs sm:text-sm">
            <a class="link link-hover">À propos</a>
            <a class="link link-hover">Contact</a>
            <a class="link link-hover hidden sm:inline">Mentions légales</a>
            <a class="link link-hover hidden sm:inline">Politique de confidentialité</a>
          </nav>
          <nav>
            <div class="grid grid-flow-col gap-3 sm:gap-4">
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a>
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path></svg></a>
              <a class="hover:scale-110 transition-transform"><svg class="w-5 h-5 sm:w-6 sm:h-6 fill-current" viewBox="0 0 24 24"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path></svg></a>
            </div>
          </nav>
          <aside>
            <p id="copyright-year" class="text-xs sm:text-sm">Copyright © 2024 - Tous droits réservés par MonSite</p>
          </aside>
        </footer>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
