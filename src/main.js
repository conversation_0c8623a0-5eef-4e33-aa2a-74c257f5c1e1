console.log('Hello World from main.js')
//* On est dans /src/main.js

// ? Exercice : Rendre Dynamique le nom du site
const siteName = "Jefff.js";

const siteNameNavbarElement = document.getElementById('site-name-navbar');
console.log(siteNameNavbarElement);
//!Paranoïa : on vérifie si l'élément est bien sélectionné
if (siteNameNavbarElement) {
  siteNameNavbarElement.textContent = siteName;
}

// ? Exercice : Rendre Dynamique la date (année) du copyright
const copyrightElement = document.getElementById('copyright-year');
//!Paranoïa : on vérifie si l'élément est bien sélectionné
if (copyrightElement) {
  const currentYear = new Date().getFullYear();
  copyrightElement.textContent = `Copyright © ${currentYear} - Tous droits réservés par ${siteName}`;
}
//---------------------------------------------------------------------------
//----------------------- LESSON DOM EVENTS--------------------------------
//---------------------------------------------------------------------------
//! Exercice : DOM Events (on click sur le main title cela modifie son texte)
const mainTitleElement = document.getElementById('main-title');
// console.log(mainTitleElement);
// mainTitleElement.addEventListener('click', () => {
//   mainTitleElement.textContent = 'Trop un truc De Botch le JS 🫠';
// });
//! Exercice : DOM Events (on click sur le main title cela modifie son texte) version avec Booleen
// let isClicked = false;
// mainTitleElement.addEventListener('click', () => {
//     mainTitleElement.textContent = isClicked ? 'Trop un truc De Botch le JS 🫠' : 'Les DOM Events en JavaScript';
//     isClicked = !isClicked;
// })
