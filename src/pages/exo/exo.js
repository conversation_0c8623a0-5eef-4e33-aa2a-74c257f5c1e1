console.log('Exo.js');
//---------------------------------------------------------------------------
//----------------------- EXERCICES DOM EVENTS--------------------------------
//---------------------------------------------------------------------------

// const btnClicMeElement = document.querySelector('#btn-clic-me');
// const inputTextElement = document.querySelector('#input-text');
// const renderKeyElement = document.querySelector('#renderKey');
// inputTextElement.addEventListener('keyup', (eventDuclavier) => {
//     console.log(eventDuclavier);
//     console.log('eventDuclavier.key', eventDuclavier.key);
//     // ok mais bof car car interprète aussi les tab backspace etc...
//     // renderKeyElement.textContent += eventDuclavier.key; 
//     renderKeyElement.textContent += eventDuclavier.target.value;
//     // if(inputTextElement.value.length >= 5){
//     //     btnClicMeElement.disabled = true;
//     // }else{
//     //     btnClicMeElement.disabled = false;
//     // }
//     // Ou version optimisée avec condition ternaire
//     btnClicMeElement.disabled = inputTextElement.value.length >= 5 ? true : false;
// });

// const monTextArea = document.querySelector('#formMessage');
// const monBtn = document.querySelector('#formSubmitBtn');
// console.log(monTextArea);
// console.log(monBtn);

// monTextArea.addEventListener('keyup',(event)=>{
//     // console.log(event);
//     // ? Mode cond ternaires
//     monBtn.disabled = monTextArea.value.length>=5 ? true : false;
//     // ? Mode IF classique 
//     // if(monTextArea.value.length>=5){
//     //    monBtn.disabled = true;
//     // }
//     // else{
//     //     monBtn.disabled = false
//     // }
// });

//---------------------------------------------------------------------------
//----------------------- EXERCICES DOM EVENTS Local Storage ----------------
//---------------------------------------------------------------------------
const inputDomElement  = document.querySelector('#input-text');
const renderKeyElementDom = document.querySelector('#renderKey');
inputDomElement.value = localStorage.getItem('monSuperTexte');
renderKeyElementDom.textContent = localStorage.getItem('monSuperTexte');

 inputDomElement.addEventListener('keyup', (event) => {
    console.log(event);
    
    renderKeyElementDom.textContent += event.target.value;
    localStorage.setItem('monSuperTexte', event.target.value);
    renderKeyElementDom.innerText = localStorage.getItem('monSuperTexte');
 });
